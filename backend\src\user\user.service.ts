import { Common, User } from "@commune/api";
import {
    BadRequestException,
    ForbiddenException,
    Injectable,
} from "@nestjs/common";
import {
    toPrismaLocalizations,
    toPrismaLocalizationsWhere,
    toPrismaPagination,
} from "src/utils";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { UserNameGeneratorService } from "./user-name-generator/user-name-generator.service";

export type CreateUser = {
    referrerId: string | null;
    email: string;
};

@Injectable()
export class UserService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
        private readonly userNameGeneratorService: UserNameGeneratorService,
    ) {}

    async getUsers(input: User.GetUsersInput, currentUser: CurrentUser) {
        const { ids, query } = input;

        const users = await this.prisma.user.findMany({
            ...toPrismaPagination(input.pagination),
            where: Object.assign(
                {},
                ids && { id: { in: ids } },
                query && {
                    OR: [
                        {
                            id: query,
                        },
                        {
                            name: toPrismaLocalizationsWhere(query),
                        },
                        {
                            description: toPrismaLocalizationsWhere(query),
                        },
                    ],
                },
            ),
            select: {
                id: true,
                role: true,
                email: true,
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: currentUser.isAdmin,
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return users;
    }

    async getUserByEmail(email: string) {
        return await this.prisma.user.findUnique({
            where: { email, deletedAt: null },
        });
    }

    async getUser(id: string, currentUser: CurrentUser) {
        const [user] = await this.getUsers(
            {
                ids: [id],
                pagination: { page: 1, size: 1 },
            },
            currentUser,
        );

        return user;
    }

    async createUser(data: CreateUser) {
        {
            const user = await this.getUserByEmail(data.email);

            if (user) {
                throw new BadRequestException(
                    ...getError("user_email_is_busy"),
                );
            }
        }

        const user = await this.prisma.user.create({
            data: {
                referrerId: data.referrerId,
                email: data.email,
                name: {
                    create: toPrismaLocalizations(
                        this.userNameGeneratorService.generateUserName(),
                        "name",
                    ),
                },
            },
        });

        return user;
    }

    async updateUser(input: User.UpdateUserInput, currentUser: CurrentUser) {
        if (!currentUser.isAdmin) {
            if (currentUser.id !== input.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_self"),
                );
            }
        }

        return await this.prisma.user.update({
            where: { id: input.id },
            data: {
                name: input.name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: input.description && {
                    deleteMany: {},
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });
    }

    async updateUserImage(
        userId: string,
        file: FileInfo,
        currentUser: CurrentUser,
    ) {
        await this.prisma.user.findUniqueOrThrow({
            where: { id: userId, deletedAt: null },
        });

        if (!currentUser.isAdmin) {
            if (currentUser.id !== userId) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_self"),
                );
            }
        }

        return await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadImage(
                file,
                "user",
                userId,
            );

            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });

            await trx.user.update({
                where: { id: userId },
                data: {
                    imageId: image.id,
                },
            });

            return image;
        });
    }
}
