import type { Infer } from "./types";

import { z } from "zod";
import {
    id,
    email,
    imageUrl,
    searchIds,
    searchQuery,
    createdAt,
    updatedAt,
    deletedAt,
    LocalizationsSchema,
    PaginationSchema,
    LocalizationLocaleSchema,
} from "./common";

export const userName = LocalizationsSchema.min(1);
export const userDescription = LocalizationsSchema;
export const userImage = imageUrl.nullable();

export const userTitleName = LocalizationsSchema.min(1);
export const userTitleIsActive = z.boolean();
export const userTitleColor = z.string().nonempty().nullable();

export const userNoteText = z.string().nonempty();

export type UserRole = Infer<typeof UserRoleSchema>;
export const UserRoleSchema = z.enum([
    "admin",
    "moderator",
    "user",
]);

export type SimpleUser = Infer<typeof SimpleUserSchema>;
export const SimpleUserSchema = z.object({
    id,
    name: userName,
    image: userImage,
});

export type GetMeOutput = Infer<typeof GetMeOutputSchema>;
export const GetMeOutputSchema = z.object({
    id,
    email,
    role: UserRoleSchema,

    name: userName,
    description: userDescription,

    image: imageUrl.nullable(),

    createdAt,
    updatedAt,
});

export type GetUsersInput = Infer<typeof GetUsersInputSchema>;
export const GetUsersInputSchema = z
    .object({
        pagination: PaginationSchema,

        ids: searchIds,
        query: searchQuery,
    })
    .partial();

export type GetUserOutput = Infer<typeof GetUserOutputSchema>;
export const GetUserOutputSchema = z.object({
    id,
    role: UserRoleSchema,

    name: userName,
    description: userDescription,

    image: userImage,

    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional(),
});

export type GetUsersOutput = Infer<typeof GetUsersOutputSchema>;
export const GetUsersOutputSchema = z.array(GetUserOutputSchema);

export type UpdateUserInput = Infer<typeof UpdateUserInputSchema>;
export const UpdateUserInputSchema = z
    .object({
        id,

        name: userName.optional(),
        description: userDescription.optional(),
    });


export type CreateUserTitleInput = Infer<typeof CreateUserTitleInputSchema>;
export const CreateUserTitleInputSchema = z.object({
    userId: id,

    name: userTitleName,

    isActive: userTitleIsActive,
    color: userTitleColor,
});

export type UpdateUserTitleInput = Infer<typeof UpdateUserTitleInputSchema>;
export const UpdateUserTitleInputSchema = z
    .object({
        id,

        name: userTitleName.optional(),

        isActive: userTitleIsActive.optional(),
        color: userTitleColor.optional(),
    });

export type GetUserTitlesInput = Infer<typeof GetUserTitlesInputSchema>;
export const GetUserTitlesInputSchema = z.object({
    userId: id,
    ids: searchIds.optional(),
    isActive: userTitleIsActive.optional(),
});

export type GetUserTitlesOutput = Infer<typeof GetUserTitlesOutputSchema>;
export const GetUserTitlesOutputSchema = z.array(
    z.object({
        id,
    
        userId: id,

        name: userTitleName,
    
        isActive: userTitleIsActive,
        color: userTitleColor,
    
        createdAt,
        updatedAt,
        deletedAt: deletedAt.optional(),
    }),
);

export type GetUserNoteInput = Infer<typeof GetUserNoteInputSchema>;
export const GetUserNoteInputSchema = z.object({
    userId: id,
});

export type GetUserNoteOutput = Infer<typeof GetUserNoteOutputSchema>;
export const GetUserNoteOutputSchema = z.object({
    text: userNoteText.nullable(),
});

export type UpdateUserNoteInput = Infer<typeof UpdateUserNoteInputSchema>;
export const UpdateUserNoteInputSchema = z.object({
    userId: id,

    text: userNoteText.nullable(),
});

export type GetUserInvitesInput = Infer<typeof GetUserInvitesInputSchema>;
export const GetUserInvitesInputSchema = z.object({
    pagination: PaginationSchema,
});

export type GetUserInvitesOutput = Infer<typeof GetUserInvitesOutputSchema>;
export const GetUserInvitesOutputSchema = z.array(z.object({
    id,
    email,
    name: z.string().nonempty().nullable(),
    locale: LocalizationLocaleSchema,
    isUsed: z.boolean(),
}));

export type UpsertUserInviteInput = Infer<typeof UpsertUserInviteInputSchema>;
export const UpsertUserInviteInputSchema = z.object({
    email,
    name: z.string().nonempty().nullable(),
    locale: LocalizationLocaleSchema,
});

export type DeleteUserInviteInput = Infer<typeof DeleteUserInviteInputSchema>;
export const DeleteUserInviteInputSchema = z.object({
    id,
});
