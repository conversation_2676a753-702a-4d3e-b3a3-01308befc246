import type { UserConfig } from "vite";

import { sveltekit } from "@sveltejs/kit/vite";
import { defineConfig, loadEnv } from "vite";

export default defineConfig((env) => {
  const { BACKEND_HOST, MINIO_HOST } = loadEnv(env.mode, process.cwd());

  return {
    plugins: [sveltekit()],
    server: {
      port: 3000,
      proxy: {
        "/images": {
          target: MINIO_HOST,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/images/, ""),
        },
        "/api": {
          target: BACKEND_HOST,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
  } satisfies UserConfig;
});
